import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import os
from datetime import datetime, timedelta
from docx import Document

# --- Core Functions ---

def generate_date_range(start_date_str, end_date_str):
    """
    Generates a list of dates between the start and end dates.
    """
    try:
        start_date = datetime.strptime(start_date_str, "%d.%m.%Y")
        end_date = datetime.strptime(end_date_str, "%d.%m.%Y")
        date_list = []
        current_date = start_date
        while current_date <= end_date:
            date_list.append(current_date)
            current_date += timedelta(days=1)
        return date_list
    except ValueError:
        return None

def update_header_date(doc, new_date_str):
    """
    Finds and replaces the date in the header of a Word document.
    """
    for section in doc.sections:
        header = section.header
        for paragraph in header.paragraphs:
            if "Date:" in paragraph.text:
                # Clear existing text and add new date
                paragraph.text = f"Date: {new_date_str}"
                # You can also use runs for more complex formatting if needed
                # run = paragraph.runs
                # if run:
                #     run[0].text = f"Date: {new_date_str}"
                break

def process_documents(template_path, start_date_str, end_date_str, output_folder, log_widget):
    """
    Main processing function to generate documents for a given date range.
    """
    log_widget.insert(tk.END, f"Starting document generation...\n")
    log_widget.insert(tk.END, f"Output Folder: {output_folder}\n")

    # 1. Generate Date Range
    dates = generate_date_range(start_date_str, end_date_str)
    if dates is None:
        messagebox.showerror("Error", "Invalid date format. Please use dd.mm.yyyy.")
        log_widget.insert(tk.END, "Error: Invalid date format.\n")
        return

    # 2. Ensure output folder exists
    if not os.path.exists(output_folder):
        try:
            os.makedirs(output_folder)
            log_widget.insert(tk.END, f"Created output folder: {output_folder}\n")
        except OSError as e:
            messagebox.showerror("Error", f"Could not create output folder:\n{e}")
            return
            
    # 3. Loop and Generate Documents
    for date in dates:
        try:
            date_str = date.strftime("%d.%m.%Y")
            log_widget.insert(tk.END, f"Processing date: {date_str}...\n")
            
            doc = Document(template_path)
            update_header_date(doc, date_str)
            
            output_filename = os.path.join(output_folder, f"{date_str}.docx")
            doc.save(output_filename)

            log_widget.insert(tk.END, f"  -> Saved: {output_filename}\n")
            log_widget.see(tk.END)
            
        except Exception as e:
            error_msg = f"Error processing {date_str}: {e}\n"
            log_widget.insert(tk.END, error_msg)
            messagebox.showerror("Processing Error", error_msg)
            break

    log_widget.insert(tk.END, "Generation complete.\n")
    messagebox.showinfo("Success", "All documents have been generated successfully!")

# --- GUI Setup ---

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Word Template Batch Generator")
        self.root.geometry("650x450")

        self.template_path = tk.StringVar()
        self.output_folder = tk.StringVar(value=os.path.join(os.getcwd(), "Generated_Documents"))

        # --- Frame for Inputs ---
        input_frame = tk.Frame(root, padx=10, pady=10)
        input_frame.pack(fill="x", padx=10, pady=5)

        # Template Selection
        tk.Label(input_frame, text="Word Template:").grid(row=0, column=0, sticky="w", pady=5)
        tk.Entry(input_frame, textvariable=self.template_path, width=60).grid(row=0, column=1, sticky="ew")
        tk.Button(input_frame, text="Browse...", command=self.select_template).grid(row=0, column=2, padx=5)
        
        # Output Folder Selection
        tk.Label(input_frame, text="Output Folder:").grid(row=1, column=0, sticky="w", pady=5)
        tk.Entry(input_frame, textvariable=self.output_folder, width=60).grid(row=1, column=1, sticky="ew")
        tk.Button(input_frame, text="Browse...", command=self.select_output_folder).grid(row=1, column=2, padx=5)

        # Date Range
        tk.Label(input_frame, text="Start Date (dd.mm.yyyy):").grid(row=2, column=0, sticky="w", pady=5)
        self.start_date_entry = tk.Entry(input_frame)
        self.start_date_entry.grid(row=2, column=1, sticky="ew", columnspan=2)
        
        tk.Label(input_frame, text="End Date (dd.mm.yyyy):").grid(row=3, column=0, sticky="w", pady=5)
        self.end_date_entry = tk.Entry(input_frame)
        self.end_date_entry.grid(row=3, column=1, sticky="ew", columnspan=2)

        input_frame.columnconfigure(1, weight=1)

        # --- Generate Button ---
        generate_button = tk.Button(root, text="Generate Documents", command=self.run_generation, bg="#4CAF50", fg="white", font=("Arial", 12, "bold"))
        generate_button.pack(pady=10, padx=10, fill="x")

        # --- Status Log ---
        log_frame = tk.Frame(root, padx=10, pady=5)
        log_frame.pack(fill="both", expand=True)
        tk.Label(log_frame, text="Progress Log:").pack(anchor="w")
        self.log_widget = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=10, state='normal')
        self.log_widget.pack(fill="both", expand=True)

    def select_template(self):
        path = filedialog.askopenfilename(
            title="Select Word Template",
            filetypes=(("Word Documents", "*.docx"), ("All files", "*.*"))
        )
        if path:
            self.template_path.set(path)

    def select_output_folder(self):
        path = filedialog.askdirectory(title="Select Output Folder")
        if path:
            self.output_folder.set(path)
            
    def run_generation(self):
        if not all([self.template_path.get(), self.output_folder.get(), self.start_date_entry.get(), self.end_date_entry.get()]):
            messagebox.showerror("Error", "All fields are required.")
            return
        
        self.log_widget.delete('1.0', tk.END)

        process_documents(
            template_path=self.template_path.get(),
            start_date_str=self.start_date_entry.get(),
            end_date_str=self.end_date_entry.get(),
            output_folder=self.output_folder.get(),
            log_widget=self.log_widget
        )

# --- Main Execution ---
if __name__ == "__main__":
    # To run this script:
    # 1. Make sure python-docx is installed: pip install python-docx
    # 2. Save the code as a .py file.
    # 3. Run from the command line: python your_script_name.py
    
    root = tk.Tk()
    app = App(root)
    root.mainloop()